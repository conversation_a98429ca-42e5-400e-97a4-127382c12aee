from typing import List, Optional, Dict, Any
try:
    from pydantic import BaseModel, Field
except ImportError:
    # Fallback to pydantic v1 compatibility if needed
    from pydantic.v1 import BaseModel, Field

class ShapeRequirement(BaseModel):
    shape_type: str
    dimensions: Dict[str, Optional[float]]
    position: Optional[List[Optional[float]]] = None
    rotation: Optional[List[Optional[float]]] = None

class ExtractedShapeInfo(BaseModel):
    shape_class: Optional[str] = Field(None, description="The general class or category of the primary shape, if identifiable (e.g., 'Perforated sheet', 'Tole', 'Countersink hole'). Should be one of the known classes. Null if not identifiable.")
    shape_model_code: Optional[str] = Field(None, description="A specific model code or identifier associated with the shape_class, if provided by the user (e.g., 'DFM', 'ABC-123'). Null if not provided or not applicable.")

class Operation(BaseModel):
    operation_type: str = Field(description="Boolean operation type (cut, fuse, common, chamfer, fillet)")
    base_shape: str = Field(description="Name of the base shape")
    tool_shape: Optional[str] = Field(None, description="Name of the tool shape (optional for chamfer/fillet operations)")
    result_name: str = Field(description="Name of the result after the operation")

class DesignRequirements(BaseModel):
    title: str = Field(description="Brief title describing the design")
    shapes: List[ShapeRequirement] = Field(description="List of required shapes")
    operations: Optional[List[Operation]] = Field(None, description="List of Boolean operations to perform")
    comments: Optional[str] = Field(None, description="Additional comments or instructions")
    complexity_level: int = Field(description="Design complexity level (1-5)")
    shape_class: Optional[str] = Field(None, description="The general class or category of the primary shape, if identifiable (e.g., 'Perforated sheet', 'Tole', 'Countersink hole'). This is used to locate a specific parameter dictionary.")
    shape_model_code: Optional[str] = Field(None, description="A specific model code or identifier within the shape_class, if provided by the user (e.g., 'ABC', 'XYZ-123'). This is used as a key in the parameter dictionary.")

class AnalysisAndParameterCheckOutput(DesignRequirements):
    missing_info: bool = Field(description="Whether there is missing information to proceed with CAD generation.")
    questions: List[str] = Field(default_factory=list, description="List of questions to ask the user to get missing information.")
    explanation: Optional[str] = Field(None, description="Brief explanation about missing information, if any.") 