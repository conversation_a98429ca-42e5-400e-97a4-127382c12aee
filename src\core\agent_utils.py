import os
import json
import re
import csv
import functools
import langdetect
import logging
from typing import List, Dict, Any
from pathlib import Path
from langchain_core.documents import Document

from .models import AnalysisAndParameterCheckOutput, ShapeRequirement, DesignRequirements
from src.utils import path_manager

logger = logging.getLogger(__name__)

def parse_unified_analysis(json_str: str) -> AnalysisAndParameterCheckOutput:
    """Convert JSON string to AnalysisAndParameterCheckOutput Pydantic model"""
    try:
        if "```json" in json_str:
            json_str = re.search(r'```json\s*(.*?)\s*```', json_str, re.DOTALL).group(1)

        data = json.loads(json_str)

        # Fix common dimension validation issues BEFORE Pydantic validation
        if "shapes" in data:
            for shape in data["shapes"]:
                if "dimensions" in shape and shape["dimensions"]:
                    # Ensure all dimension values are numeric
                    for key, value in list(shape["dimensions"].items()):
                        if value is None or not isinstance(value, (int, float)):
                            shape["dimensions"][key] = 10.0  # Default fallback value
                            logger.warning(f"Fixed null/invalid dimension {key} with default value 10.0")

        return AnalysisAndParameterCheckOutput(**data)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON string in parse_unified_analysis: {e}")
        return AnalysisAndParameterCheckOutput(
            title="Invalid JSON format received",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})],
            complexity_level=1,
            missing_info=True,
            questions=["The design requirements were not in a valid JSON format. Can you describe it again?"],
            explanation=f"JSON decoding error: {e}"
        )
    except Exception as e:
        logger.error(f"Error converting JSON to AnalysisAndParameterCheckOutput (likely Pydantic validation): {e}")

        # Try to extract and fix the JSON data if possible
        try:
            if "```json" in json_str:
                json_str = re.search(r'```json\s*(.*?)\s*```', json_str, re.DOTALL).group(1)
            data = json.loads(json_str)

            # Fix common dimension validation issues
            if "shapes" in data:
                for shape in data["shapes"]:
                    if "dimensions" in shape and shape["dimensions"]:
                        # Ensure all dimension values are numeric
                        for key, value in list(shape["dimensions"].items()):
                            if value is None or not isinstance(value, (int, float)):
                                shape["dimensions"][key] = 10.0  # Default fallback value
                                logger.warning(f"Fixed null/invalid dimension {key} with default value 10.0")

            # Try again with fixed data
            return AnalysisAndParameterCheckOutput(**data)
        except Exception as fix_error:
            # If fixing fails, use complete fallback
            logger.error(f"Failed to fix JSON data: {fix_error}")
            pass

        return AnalysisAndParameterCheckOutput(
            title="Potentially Incomplete Requirements (Parsing Issues)",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})],
            operations=None,
            comments="Fallback due to parsing issues.",
            complexity_level=1,
            shape_class=None,
            shape_model_code=None,
            missing_info=True,
            questions=["Some details might be unclear due to formatting. Please review the generated code carefully, or try rephrasing your request if the result is not as expected."],
            explanation=f"Pydantic validation or other parsing error: {str(e)}"
        )

def parse_unified_output(json_str: str) -> AnalysisAndParameterCheckOutput:
    """Convert JSON string to AnalysisAndParameterCheckOutput Pydantic model"""
    try:
        if "```json" in json_str:
            json_str = re.search(r'```json\s*(.*?)\s*```', json_str, re.DOTALL).group(1)

        data = json.loads(json_str)

        # Fix common dimension validation issues BEFORE Pydantic validation
        if "shapes" in data:
            for shape in data["shapes"]:
                if "dimensions" in shape and shape["dimensions"]:
                    # Ensure all dimension values are numeric
                    for key, value in list(shape["dimensions"].items()):
                        if value is None or not isinstance(value, (int, float)):
                            shape["dimensions"][key] = 10.0  # Default fallback value
                            logger.warning(f"Fixed null/invalid dimension {key} with default value 10.0")

        return AnalysisAndParameterCheckOutput(**data)
    except json.JSONDecodeError as e:
        logger.error(f"Error decoding JSON string in parse_unified_output: {e}")
        return AnalysisAndParameterCheckOutput(
            title="Invalid JSON format received",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})],
            complexity_level=1,
            missing_info=True,
            questions=["The design requirements were not in a valid JSON format. Can you describe it again?"],
            explanation=f"JSON decoding error: {e}"
        )
    except Exception as e:
        logger.error(f"Error converting JSON to AnalysisAndParameterCheckOutput (likely Pydantic validation): {e}")

        # Try to extract and fix the JSON data if possible
        try:
            if "```json" in json_str:
                json_str = re.search(r'```json\s*(.*?)\s*```', json_str, re.DOTALL).group(1)
            data = json.loads(json_str)

            # Fix common dimension validation issues
            if "shapes" in data:
                for shape in data["shapes"]:
                    if "dimensions" in shape and shape["dimensions"]:
                        # Ensure all dimension values are numeric
                        for key, value in shape["dimensions"].items():
                            if value is None or not isinstance(value, (int, float)):
                                shape["dimensions"][key] = 10.0  # Default fallback value

            # Try again with fixed data
            return AnalysisAndParameterCheckOutput(**data)
        except:
            # If fixing fails, use complete fallback
            pass

        return AnalysisAndParameterCheckOutput(
            title="Potentially Incomplete Requirements (Parsing Issues)",
            shapes=[ShapeRequirement(shape_type="box", dimensions={"length": 10.0, "width": 10.0, "height": 10.0})],
            operations=None,
            comments="Fallback due to parsing issues.",
            complexity_level=1,
            shape_class=None,
            shape_model_code=None,
            missing_info=True,
            questions=["Some details might be unclear due to formatting. Please review the generated code carefully, or try rephrasing your request if the result is not as expected."],
            explanation=f"Pydantic validation or other parsing error: {str(e)}"
        )

def clean_code(code_str: str) -> str:
    """Clean up the code string"""
    if "```python" in code_str:
        code_str = re.search(r'```python\s*(.*?)\s*```', code_str, re.DOTALL).group(1)
    return code_str.strip()

def create_rag_query(design_reqs: DesignRequirements) -> str:
    """Creates a focused RAG query based on analyzed design requirements."""
    shape_types = []
    if design_reqs.shapes:
        shape_types = list(set([s.shape_type for s in design_reqs.shapes]))

    operation_types = []
    if design_reqs.operations:
        operation_types = list(set([o.operation_type for o in design_reqs.operations]))

    query_parts = ["FreeCAD Python script"]
    if shape_types:
        query_parts.append(f"for creating {' and '.join(shape_types)}")
    if operation_types:
        query_parts.append(f"using operations like {' and '.join(operation_types)}")

    query = " ".join(query_parts)

    if not shape_types and not operation_types:
        fallback_term = design_reqs.title if design_reqs.title else 'CAD modeling'
        return f"FreeCAD Python script for {fallback_term}"

    return query

def format_retrieved_context(docs: List[Document]) -> str:
    """Formats the retrieved list of documents (from FAISS) into a single string."""
    if not docs:
        return "No relevant context found."

    context_str = ""
    for i, doc in enumerate(docs):
        if isinstance(doc, Document):
            content = doc.page_content
            source = doc.metadata.get('source', 'Local Guide')
            context_str += f"--- Context Source {i+1} ({source}) ---\n{content}\n\n"
        else:
            print(f"Warning: Unexpected document type in format_retrieved_context: {type(doc)}")
            context_str += f"--- Context Source {i+1} (Unknown Source) ---\n{str(doc)}\n\n"

    return context_str.strip()

def detect_language(text):
    """Detect language from input text"""
    try:
        return langdetect.detect(text)
    except:
        return "en"

def get_success_message(user_text):
    """Return a success message based on the user's language"""
    lang = detect_language(user_text)
    
    success_messages = {
        "en": "FreeCAD code generation successful.",
        "vi": "Tạo mã FreeCAD thành công.",
        "fr": "Génération de code FreeCAD réussie.",
        "es": "Generación de código FreeCAD exitosa.",
        "de": "FreeCAD-Code erfolgreich generiert.",
        "zh-cn": "FreeCAD代码生成成功。",
        "ja": "FreeCADコードの生成に成功しました。",
        "ko": "FreeCAD 코드 생성 성공.",
    }
    
    return success_messages.get(lang, success_messages["en"])

def combine_and_format_contexts(inputs: Dict[str, Any]) -> Dict[str, Any]:
    """Formats the local context."""
    combined_docs = inputs.get("local_context", [])
    formatted_context = format_retrieved_context(combined_docs)

    design_requirements = inputs["design_requirements"]
    sanitized_title = re.sub(r'[^\w\s-]', '', design_requirements.title).strip()
    sanitized_title = ''.join(c for c in sanitized_title if ord(c) < 128)
    sanitized_title = sanitized_title.replace(' ', '_')
    if not sanitized_title:
        sanitized_title = "generated_cad"

    return {
        "design_requirements": design_requirements,
        "retrieved_context": formatted_context,
        "sanitized_title": sanitized_title
    }

@functools.lru_cache(maxsize=None)
def get_dynamic_guidance_from_dictionary(shape_class: str = None, shape_model_code: str = None) -> str:
    """Get dynamic guidance from dictionary.csv based on shape class and model code"""
    if not shape_class:
        return ""

    if not shape_model_code:
        print(f"DEBUG: No shape_model_code provided for shape_class '{shape_class}'. No specific model guidance will be retrieved.")
        return ""

    dict_path = path_manager.DATA_DIR / "Class" / shape_class / "dictionary.csv"
    guidance = ""
    print(f"DEBUG: Attempting to read dictionary for dynamic guidance from: {dict_path}")

    if dict_path.exists():
        try:
            with open(dict_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader, None)

                if headers:
                    name_col_idx = -1
                    params_col_idx = -1
                    desc_col_idx = -1

                    for i, h_val in enumerate(headers):
                        h_lower = h_val.strip().lower()
                        if h_lower in ['name', 'id', 'code', 'model', 'modelcode', 'model_code']:
                            name_col_idx = i
                        elif h_lower in ['parameters', 'params', 'parameter']:
                            params_col_idx = i
                        elif h_lower in ['description', 'desc', 'detail', 'description']:
                            desc_col_idx = i

                    if name_col_idx == -1: name_col_idx = 0
                    if params_col_idx == -1: params_col_idx = 1

                    if name_col_idx == -1 or params_col_idx == -1:
                        print(f"DEBUG: Could not reliably determine Name/ID and Parameters columns in {dict_path}")
                        return ""

                    actual_parameters_for_model = []
                    model_description = ""

                    for row_data in reader:
                        if row_data and \
                           len(row_data) > name_col_idx and \
                           len(row_data) > params_col_idx and \
                           row_data[name_col_idx].strip().upper() == shape_model_code.strip().upper():

                            params_string = row_data[params_col_idx].strip()
                            if params_string:
                                actual_parameters_for_model = [p.strip() for p in params_string.split(',') if p.strip()]

                            if desc_col_idx != -1 and len(row_data) > desc_col_idx:
                                model_description = row_data[desc_col_idx].strip()

                            break

                    guidance_parts = []

                    if actual_parameters_for_model:
                        guidance_parts.append(
                            f"For the specified model '{shape_model_code}' of type '{shape_class}', "
                            f"the following parameters are typically required: {', '.join(actual_parameters_for_model)}. "
                            "Please provide them if you haven't already."
                        )
                        print(f"DEBUG: Dynamic params guidance generated.")

                    if model_description:
                        guidance_parts.append(f"Description for this model: {model_description}")
                        print(f"DEBUG: Dynamic description guidance generated.")

                    guidance = " ".join(guidance_parts).strip()

                    if not guidance:
                        print(f"DEBUG: Model code '{shape_model_code}' not found or has no parameters/description in {dict_path} (or parameters/description string was empty).")

                else:
                    print(f"DEBUG: Dictionary file {dict_path} is empty or has no headers.")
        except FileNotFoundError:
            print(f"DEBUG: Dictionary file not found (though exists() was true?): {dict_path}")
        except Exception as e:
            print(f"Error reading or parsing dictionary.csv for {shape_class}/{shape_model_code}: {e}")
    else:
        print(f"DEBUG: Dictionary file path does not exist: {dict_path}")

    return guidance 