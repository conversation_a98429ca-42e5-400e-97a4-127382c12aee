#!/usr/bin/env python3
"""
Test cases for the new countersink hole workflow.

This script tests the new workflow:
1. Countersink type (Head screw or Countersunk screw)
2. Hole type (blind or through)
3. Countersink angle (if countersunk screw)
4. Thread size
5. Auto-extract dimensions
6. Hole depth (if blind)
7. Validation
"""

import sys
import os
import time

# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.chatbot import process_user_request

def test_countersink_workflow():
    """Test the complete countersink workflow with various scenarios."""
    
    print("="*80)
    print("TESTING NEW COUNTERSINK HOLE WORKFLOW")
    print("="*80)
    
    # Test Case 1: Basic countersink request - should ask for countersink type first
    print("\n" + "="*60)
    print("TEST CASE 1: Basic countersink request")
    print("="*60)
    
    test_message_1 = "Create a rectangular block 50x30x10mm with a countersink hole in the center"
    print(f"Input: {test_message_1}")
    
    result_1 = process_user_request(test_message_1, is_edit_request=False, request_origin='test')
    print(f"Result: {result_1}")
    
    time.sleep(2)  # Brief pause between tests
    
    # Test Case 2: Specify countersink type - should ask for hole type
    print("\n" + "="*60)
    print("TEST CASE 2: Specify countersink type")
    print("="*60)
    
    test_message_2 = "Head screw"
    print(f"Input: {test_message_2}")
    
    result_2 = process_user_request(test_message_2, is_edit_request=False, request_origin='test')
    print(f"Result: {result_2}")
    
    time.sleep(2)
    
    # Test Case 3: Specify hole type - should ask for thread size (since Head screw doesn't need angle)
    print("\n" + "="*60)
    print("TEST CASE 3: Specify hole type for head screw")
    print("="*60)
    
    test_message_3 = "Through"
    print(f"Input: {test_message_3}")
    
    result_3 = process_user_request(test_message_3, is_edit_request=False, request_origin='test')
    print(f"Result: {result_3}")
    
    time.sleep(2)
    
    # Test Case 4: Specify thread size - should auto-extract dimensions and generate code
    print("\n" + "="*60)
    print("TEST CASE 4: Specify thread size for head screw")
    print("="*60)
    
    test_message_4 = "M6"
    print(f"Input: {test_message_4}")
    
    result_4 = process_user_request(test_message_4, is_edit_request=False, request_origin='test')
    print(f"Result: {result_4}")
    
    time.sleep(2)
    
    # Test Case 5: Countersunk screw workflow - should ask for angle
    print("\n" + "="*60)
    print("TEST CASE 5: Countersunk screw workflow")
    print("="*60)
    
    test_message_5 = "Create a rectangular block 40x40x5mm with a countersink hole. Countersunk screw, blind hole"
    print(f"Input: {test_message_5}")
    
    result_5 = process_user_request(test_message_5, is_edit_request=False, request_origin='test')
    print(f"Result: {result_5}")
    
    time.sleep(2)
    
    # Test Case 6: Specify angle for countersunk screw
    print("\n" + "="*60)
    print("TEST CASE 6: Specify countersink angle")
    print("="*60)
    
    test_message_6 = "82°"
    print(f"Input: {test_message_6}")
    
    result_6 = process_user_request(test_message_6, is_edit_request=False, request_origin='test')
    print(f"Result: {result_6}")
    
    time.sleep(2)
    
    # Test Case 7: Specify thread size for countersunk screw
    print("\n" + "="*60)
    print("TEST CASE 7: Specify thread size for countersunk screw")
    print("="*60)
    
    test_message_7 = "M8"
    print(f"Input: {test_message_7}")
    
    result_7 = process_user_request(test_message_7, is_edit_request=False, request_origin='test')
    print(f"Result: {result_7}")
    
    time.sleep(2)
    
    # Test Case 8: Specify hole depth for blind hole
    print("\n" + "="*60)
    print("TEST CASE 8: Specify hole depth for blind hole")
    print("="*60)
    
    test_message_8 = "3mm"
    print(f"Input: {test_message_8}")
    
    result_8 = process_user_request(test_message_8, is_edit_request=False, request_origin='test')
    print(f"Result: {result_8}")
    
    print("\n" + "="*80)
    print("COUNTERSINK WORKFLOW TESTING COMPLETED")
    print("="*80)

def test_validation_scenarios():
    """Test validation scenarios for countersink holes."""
    
    print("\n" + "="*80)
    print("TESTING COUNTERSINK VALIDATION SCENARIOS")
    print("="*80)
    
    # Test Case 9: Invalid depth - should warn about exceeding material thickness
    print("\n" + "="*60)
    print("TEST CASE 9: Invalid depth validation")
    print("="*60)
    
    test_message_9 = "Create a 20x20x2mm block with M10 countersunk screw blind hole, 82°, hole depth 5mm"
    print(f"Input: {test_message_9}")
    
    result_9 = process_user_request(test_message_9, is_edit_request=False, request_origin='test')
    print(f"Result: {result_9}")
    
    time.sleep(2)
    
    # Test Case 10: Invalid angle for countersunk screw
    print("\n" + "="*60)
    print("TEST CASE 10: Invalid countersink angle")
    print("="*60)
    
    test_message_10 = "Create a 30x30x5mm block with M6 countersunk screw through hole, 45°"
    print(f"Input: {test_message_10}")
    
    result_10 = process_user_request(test_message_10, is_edit_request=False, request_origin='test')
    print(f"Result: {result_10}")
    
    print("\n" + "="*80)
    print("VALIDATION TESTING COMPLETED")
    print("="*80)

if __name__ == "__main__":
    try:
        # Run the workflow tests
        test_countersink_workflow()
        
        # Run the validation tests
        test_validation_scenarios()
        
        print("\n" + "="*80)
        print("ALL TESTS COMPLETED SUCCESSFULLY")
        print("="*80)
        
    except Exception as e:
        print(f"\nERROR during testing: {e}")
        import traceback
        traceback.print_exc()
