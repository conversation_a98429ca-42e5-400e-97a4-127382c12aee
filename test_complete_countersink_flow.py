#!/usr/bin/env python3
"""
Test complete countersink hole workflow with proper conversation flow.
This test simulates a real user conversation with the chatbot.
"""

import sys
import os
import time

# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.chatbot import process_user_request

def test_complete_head_screw_workflow():
    """Test complete workflow for head screw countersink hole."""
    
    print("="*80)
    print("TESTING COMPLETE HEAD SCREW WORKFLOW")
    print("="*80)
    
    # Test a complete specification in one message
    test_message = """Create a rectangular block 50x30x10mm with a countersink hole in the center.
    Countersink type: Head screw
    Hole type: Through
    Thread size: M6"""
    
    print(f"Input: {test_message}")
    
    result = process_user_request(test_message, is_edit_request=False, request_origin='test')
    print(f"Result: {result}")
    
    return result

def test_complete_countersunk_screw_workflow():
    """Test complete workflow for countersunk screw countersink hole."""
    
    print("\n" + "="*80)
    print("TESTING COMPLETE COUNTERSUNK SCREW WORKFLOW")
    print("="*80)
    
    # Test a complete specification in one message
    test_message = """Create a rectangular block 40x40x5mm with a countersink hole in the center.
    Countersink type: Countersunk screw
    Hole type: Blind
    Countersink angle: 82°
    Thread size: M8
    Hole depth: 2mm"""
    
    print(f"Input: {test_message}")
    
    result = process_user_request(test_message, is_edit_request=False, request_origin='test')
    print(f"Result: {result}")
    
    return result

def test_validation_scenarios():
    """Test various validation scenarios."""
    
    print("\n" + "="*80)
    print("TESTING VALIDATION SCENARIOS")
    print("="*80)
    
    # Test 1: Invalid depth
    print("\n" + "-"*60)
    print("Test 1: Invalid depth (should fail validation)")
    print("-"*60)
    
    test_message_1 = """Create a 20x20x2mm block with a countersink hole.
    Countersink type: Countersunk screw
    Hole type: Blind
    Countersink angle: 82°
    Thread size: M10
    Hole depth: 5mm"""
    
    print(f"Input: {test_message_1}")
    result_1 = process_user_request(test_message_1, is_edit_request=False, request_origin='test')
    print(f"Result: {result_1}")
    
    # Test 2: Invalid angle
    print("\n" + "-"*60)
    print("Test 2: Invalid angle (should fail validation)")
    print("-"*60)
    
    test_message_2 = """Create a 30x30x5mm block with a countersink hole.
    Countersink type: Countersunk screw
    Hole type: Through
    Countersink angle: 45°
    Thread size: M6"""
    
    print(f"Input: {test_message_2}")
    result_2 = process_user_request(test_message_2, is_edit_request=False, request_origin='test')
    print(f"Result: {result_2}")
    
    # Test 3: Valid countersunk screw with 100° angle
    print("\n" + "-"*60)
    print("Test 3: Valid countersunk screw with 100° angle")
    print("-"*60)
    
    test_message_3 = """Create a 30x30x5mm block with a countersink hole.
    Countersink type: Countersunk screw
    Hole type: Through
    Countersink angle: 100°
    Thread size: M6"""
    
    print(f"Input: {test_message_3}")
    result_3 = process_user_request(test_message_3, is_edit_request=False, request_origin='test')
    print(f"Result: {result_3}")
    
    return [result_1, result_2, result_3]

def test_dimension_extraction():
    """Test that dimensions are correctly extracted from thread size."""
    
    print("\n" + "="*80)
    print("TESTING DIMENSION EXTRACTION")
    print("="*80)
    
    # Test different thread sizes for head screws
    thread_sizes = ["M4", "M5", "M6", "M8", "M10", "M12"]
    
    for thread_size in thread_sizes:
        print(f"\n" + "-"*40)
        print(f"Testing {thread_size} Head Screw")
        print("-"*40)
        
        test_message = f"""Create a 50x50x15mm block with a countersink hole.
        Countersink type: Head screw
        Hole type: Through
        Thread size: {thread_size}"""
        
        print(f"Input: {test_message}")
        result = process_user_request(test_message, is_edit_request=False, request_origin='test')
        print(f"Result: {result}")
        
        time.sleep(1)  # Brief pause between tests

if __name__ == "__main__":
    try:
        # Test complete workflows
        head_screw_result = test_complete_head_screw_workflow()
        countersunk_screw_result = test_complete_countersunk_screw_workflow()
        
        # Test validation scenarios
        validation_results = test_validation_scenarios()
        
        # Test dimension extraction
        test_dimension_extraction()
        
        print("\n" + "="*80)
        print("SUMMARY OF RESULTS")
        print("="*80)
        
        print(f"Head Screw Test: {'✅ PASSED' if head_screw_result.get('code') else '❌ FAILED'}")
        print(f"Countersunk Screw Test: {'✅ PASSED' if countersunk_screw_result.get('code') else '❌ FAILED'}")
        
        print(f"Validation Test 1 (Invalid Depth): {'✅ PASSED' if validation_results[0].get('message') and 'depth' in validation_results[0].get('message', '').lower() else '❌ FAILED'}")
        print(f"Validation Test 2 (Invalid Angle): {'✅ PASSED' if validation_results[1].get('message') and 'angle' in validation_results[1].get('message', '').lower() else '❌ FAILED'}")
        print(f"Validation Test 3 (Valid 100°): {'✅ PASSED' if validation_results[2].get('code') else '❌ FAILED'}")
        
        print("\n" + "="*80)
        print("ALL COMPREHENSIVE TESTS COMPLETED")
        print("="*80)
        
    except Exception as e:
        print(f"\nERROR during testing: {e}")
        import traceback
        traceback.print_exc()
