{"class_name": "Countersink hole", "rules": [{"id": "CS_001", "category": "workflow_enforcement", "title": "Countersink Type Required First", "description": "Countersink type (Head screw or Countersunk screw) must be specified first", "rule": "When user requests a countersink hole, countersink type must be provided first", "severity": "error", "validation_code": "countersink_type == null || countersink_type == ''", "error_message": "Missing parameters: countersink type", "parameters": ["countersink_type"], "priority": "critical", "workflow_step": 1, "questions": ["Please specify the countersink type: Head screw or Countersunk screw"], "block_all_other_questions": true, "auto_determine": {"when": "countersink_hole_requested && countersink_type_missing", "action": "prompt_for_countersink_type", "logic": "Ask user to specify countersink type first before any other parameters"}}, {"id": "CS_002", "category": "workflow_enforcement", "title": "Hole Type Required After Countersink Type", "description": "Hole type (blind or through) must be specified after countersink type", "rule": "After countersink type is provided, hole type must be specified", "severity": "error", "validation_code": "countersink_type != null && countersink_type != '' && (hole_type == null || hole_type == '')", "error_message": "Missing parameters: hole type", "parameters": ["countersink_type", "hole_type"], "priority": "critical", "workflow_step": 2, "questions": ["Please choose the hole type: Blind or Through"], "auto_determine": {"when": "countersink_type_provided && hole_type_missing", "action": "prompt_for_hole_type", "logic": "Ask user to choose hole type after countersink type is provided"}}, {"id": "CS_003", "category": "workflow_enforcement", "title": "Countersink Angle Required for Countersunk Screws", "description": "For countersunk screws, countersink angle must be specified", "rule": "When countersink type is 'Countersunk screw', countersink angle must be provided", "severity": "error", "validation_code": "countersink_type == 'Countersunk screw' && (countersink_angle == null || countersink_angle == '')", "error_message": "Missing parameters: countersink angle", "parameters": ["countersink_type", "countersink_angle"], "priority": "critical", "workflow_step": 3, "questions": ["Please specify the countersink angle (82° or 100° for sheet metal screws)"], "auto_determine": {"when": "countersink_type == 'Countersunk screw' && countersink_angle_missing", "action": "prompt_for_countersink_angle", "logic": "Ask user to specify countersink angle when countersunk screw is selected"}}, {"id": "CS_004", "category": "workflow_enforcement", "title": "<PERSON><PERSON><PERSON> Required After Previous Steps", "description": "Thread size must be specified after countersink type, hole type, and angle (if needed)", "rule": "After required previous parameters are provided, thread size must be specified", "severity": "error", "validation_code": "((countersink_type == 'Head screw' && hole_type != null && hole_type != '') || (countersink_type == 'Countersunk screw' && hole_type != null && hole_type != '' && countersink_angle != null && countersink_angle != '')) && (thread_size == null || thread_size == '')", "error_message": "Missing parameters: thread size", "parameters": ["thread_size"], "priority": "critical", "workflow_step": 4, "questions": ["Please specify the thread size (e.g., M4, M5, M6, M8, M10, M12, M14, M16, M18, M20)"], "auto_determine": {"when": "previous_steps_complete && thread_size_missing", "action": "prompt_for_thread_size", "logic": "Ask user to specify thread size after all previous workflow steps are complete"}}, {"id": "CS_005", "category": "workflow_enforcement", "title": "Hole Depth Required for Blind Holes", "description": "For blind holes, hole depth must be specified", "rule": "When hole type is blind, hole depth must be provided", "severity": "error", "validation_code": "hole_type == 'blind' && (hole_depth == null || hole_depth <= 0)", "error_message": "Missing parameters: hole depth", "parameters": ["hole_type", "hole_depth"], "priority": "critical", "workflow_step": 5, "questions": ["Please specify the depth of the threaded hole"], "auto_determine": {"when": "hole_type == 'blind' && hole_depth_missing", "action": "prompt_for_hole_depth", "logic": "Ask user to specify hole depth when blind hole is selected"}}, {"id": "CS_006", "category": "auto_determination", "title": "Auto-determine Standard Dimensions for Head Screws", "description": "Automatically determine and override d1, d2, total_depth from thread_size for head screws", "rule": "Use standard lookup table to determine and apply dimensions from thread size for head screws", "severity": "info", "validation_code": "true", "error_message": "INFO: Standard head screw dimensions auto-applied from thread size.", "parameters": ["thread_size", "countersink_type"], "priority": "highest", "auto_determine": {"when": "thread_size is provided && countersink_type == 'Head screw'", "action": "override_existing_values", "logic": {"M4": {"d1": 4.5, "d2": 8, "total_depth": 8, "countersink_angle": 90}, "M5": {"d1": 5.5, "d2": 10, "total_depth": 10, "countersink_angle": 90}, "M6": {"d1": 6.6, "d2": 11, "total_depth": 11, "countersink_angle": 90}, "M8": {"d1": 9, "d2": 15, "total_depth": 15, "countersink_angle": 90}, "M10": {"d1": 11, "d2": 18, "total_depth": 18, "countersink_angle": 90}, "M12": {"d1": 13.5, "d2": 20, "total_depth": 20, "countersink_angle": 90}}}}, {"id": "CS_007", "category": "auto_determination", "title": "Auto-determine Standard Dimensions for Countersunk Screws", "description": "Automatically determine and override d1, d2, total_depth from thread_size for countersunk screws", "rule": "Use standard lookup table to determine and apply dimensions from thread size for countersunk screws", "severity": "info", "validation_code": "true", "error_message": "INFO: Standard countersunk screw dimensions auto-applied from thread size.", "parameters": ["thread_size", "countersink_type"], "priority": "highest", "auto_determine": {"when": "thread_size is provided && countersink_type == 'Countersunk screw'", "action": "override_existing_values", "logic": {"M4": {"d1": 4.5, "d2": 8.6, "total_depth": 2.1}, "M5": {"d1": 5.5, "d2": 10.4, "total_depth": 2.5}, "M6": {"d1": 6.6, "d2": 12.4, "total_depth": 2.9}, "M8": {"d1": 9, "d2": 16.4, "total_depth": 3.7}, "M10": {"d1": 11, "d2": 20.4, "total_depth": 4.7}, "M12": {"d1": 13.5, "d2": 24.4, "total_depth": 5.2}, "M14": {"d1": 15.5, "d2": 27.4, "total_depth": 5.7}, "M16": {"d1": 17.5, "d2": 32.4, "total_depth": 7.2}, "M18": {"d1": 20, "d2": 36.4, "total_depth": 8.2}, "M20": {"d1": 22, "d2": 40.4, "total_depth": 9.2}}}}, {"id": "CS_008", "category": "validation", "title": "Blind Hole Depth Validation", "description": "For blind holes, total depth must not exceed material thickness", "rule": "For blind holes: total_depth + hole_depth ≤ material_thickness", "severity": "error", "validation_code": "hole_type == 'blind' && (total_depth + hole_depth) > material_thickness", "error_message": "ERROR: The total depth of the countersink and the threaded hole exceeds the material thickness. This would result in a through hole, not a blind hole.", "parameters": ["hole_type", "total_depth", "hole_depth", "material_thickness"], "priority": "highest", "questions": ["Please reduce the hole depth or increase material thickness"], "auto_determine": {"when": "hole_type == 'blind' && depth_validation_fails", "action": "warn_depth_exceeds_thickness", "logic": "Warn user that total depth exceeds material thickness"}}, {"id": "CS_009", "category": "validation", "title": "Minimum Sheet Thickness", "description": "Sheet thickness must be sufficient for countersink depth", "rule": "Sheet thickness ≥ total_depth + 0.5 mm", "severity": "error", "validation_code": "material_thickness >= (total_depth + 0.5)", "error_message": "ERROR: Sheet thickness insufficient. Minimum thickness = total_depth + 0.5mm.", "parameters": ["material_thickness", "total_depth"]}, {"id": "CS_010", "category": "validation", "title": "Countersink Angle Validation", "description": "Countersink angle must be 82° or 100° for countersunk screws", "rule": "For countersunk screws, countersink angle must be 82° or 100°", "severity": "error", "validation_code": "countersink_type == 'Countersunk screw' && (countersink_angle == 82 || countersink_angle == 100)", "error_message": "ERROR: Countersunk screws require 82° or 100° countersink angle.", "parameters": ["countersink_type", "countersink_angle"]}, {"id": "CS_011", "category": "auto_correction", "title": "Auto-correct Through Hole Depth", "description": "Automatically set hole depth to full thickness for through holes", "rule": "For through holes, hole depth should equal material thickness", "severity": "info", "validation_code": "hole_type != 'through' || hole_depth == material_thickness", "error_message": "INFO: Through hole depth auto-corrected to material thickness.", "parameters": ["hole_type", "material_thickness"], "priority": "high", "auto_determine": {"when": "hole_type == 'through'", "action": "set_hole_depth_to_material_thickness", "logic": "hole_depth = material_thickness"}}]}